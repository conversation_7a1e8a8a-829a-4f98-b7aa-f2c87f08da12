"""
Main script for warehouse unloading simulation.

created on 12.04.2025 by <PERSON><PERSON><PERSON>
updated on 17.05.2025
"""

import simpy
import pandas as pd
import os
import sys
import logging

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.utils.input_parser import InputParser
from src.utils.config_parser import ConfigParser
from src.utils.results_handler import handle_inbound_results, handle_outbound_results, track_inbound_pallet_location, track_outbound_pallet_location, handle_forklift_results, handle_lift_results
from src.models.entities import Truck, Pallet
from src.models.simulation import Simulation

# setup logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
formatter = logging.Formatter('%(levelname)s - %(name)s - %(message)s')
filehandler = logging.FileHandler('./src/logs/simulation.log')
filehandler.setFormatter(formatter)
logger.addHandler(filehandler)

def main(config_path: str, inbound_input_path: str, outbound_input_path: str):
    """Main entry point for the simulation."""

    # load the configuration data
    config = ConfigParser()
    config.pass_yaml(config_path)

    # load the inbound data
    inbound_data = InputParser(config, inbound_input_path, outbound_input_path)

    # create the simulation environment
    env = simpy.Environment()

    # store the truck data
    trucks = []

    # generate entities for simulation
    for truck in inbound_data.inbound_data:
        truck_entity = Truck(env, config, truck["entity_id"], truck["store_id"], truck["unloading_port"], truck["arrival_time"], [])
        
        for pallet in truck["pallets"]:
            pallet_entity = Pallet(env, config, pallet["entity_id"], pallet["store_id"], pallet["unloading_port"], pallet["cargo_type"], pallet["location"], pallet["truck_id"], pallet["arrival_time"])
            truck_entity.pallets.append(pallet_entity)
        
        trucks.append(truck_entity)

    # store the outbound pallet data
    outbound_pallets = []

    for pallet in inbound_data.outbound_data:
        pallet_entity = Pallet(env, config, pallet["entity_id"], pallet["store_id"], pallet["unloading_port"], pallet["cargo_type"], pallet["location"], pallet["truck_id"], pallet["arrival_time"])
        outbound_pallets.append(pallet_entity)

    # create the simulation
    simulation = Simulation(env, config, trucks, outbound_pallets)
    
    # run the simulation
    env.run()

    # store the inbound simulation results
    inbound_result_path = inbound_input_path.split(".")[0] + "_output.csv"
    handle_inbound_results(simulation.inbound_pallets_track, inbound_result_path)

    # store the outbound simulation results
    outbound_result_path = outbound_input_path.split(".")[0] + "_output.csv"
    handle_outbound_results(simulation.outbound_pallets_track, outbound_result_path)

    # summary of pallet locations
    track_inbound_pallet_location(simulation.inbound_pallets_track)
    track_outbound_pallet_location(simulation.outbound_pallets_track)

    # # forklift results
    forklift_df = handle_forklift_results(simulation.inbound_pallets_track, simulation.outbound_pallets_track)

    # # lift results
    lift_df = handle_lift_results(simulation.inbound_pallets_track, simulation.outbound_pallets_track)

    # # merge the results
    result_df = pd.merge(forklift_df, lift_df, on="hour")
    result_df.to_csv(inbound_input_path.split(".")[0] + "_lift_forklift_output.csv", index=True)

if __name__ == "__main__":
    config_path = "src/config/config_test.yaml"
    inbound_input_path = "src/data/input_data_inbound_6518_v1.csv"
    outbound_input_path = "src/data/input_data_outbound_6518_v1.csv"

    main(config_path =config_path, inbound_input_path = inbound_input_path, outbound_input_path = outbound_input_path)
