# simulation start time in HH:MM:SS format
SIMULATION_START_TIME: 08:00:00

# number of DC docks
DC_DOCKS: 1
# number of DSD docks
DSD_DOCKS: 1

# time taken for a forklift to unload a pallet from truck
DC_FORKLIFT_UNLOAD_DURATION: 120
# time taken for a forklift to unload a pallet from truck
DSD_FORKLIFT_UNLOAD_DURATION: 55
# time taken to check pallets after loading/ unloading to/ from truck
DSD_PALLET_COUNT_CHECK_DURATION: 60
# time taken to travel from DC to fresh storage
TRAVEL_TIME_DC_TO_FRESH_STORAGE: 120
# time taken to travel from DC to ambient storage
TRAVEL_TIME_DC_TO_AMBIENT_STORAGE: 120
# time taken to travel from DC to lift area
TRAVEL_TIME_DC_TO_LIFT: 120
# time taken to travel from DC to lift area
TRAVEL_TIME_DSD_TO_LIFT: 60
# time taken to travel from DSD to fresh storage
TRAVEL_TIME_DSD_TO_FRESH_STORAGE: 60
# time taken to travel from DSD to ambient storage
TRAVEL_TIME_DSD_TO_AMBIENT_STORAGE: 60
# time taken for a forklift to load a pallet to lift
LIFT_FORKLIFT_LOAD_DURATION: 55
# time taken for a forklift to unload a pallet from lift
LIFT_FORKLIFT_UNLOAD_DURATION: 55
# time taken for a forklift to transport a pallet from lift to fresh storage
LIFT_FORKLIFT_TRANSPORT_DURATION_BUFFER_FRESH: 65
# time taken for a forklift to transport a pallet from lift to ambient storage
LIFT_FORKLIFT_TRANSPORT_DURATION_BUFFER_AMBIENT: 65
# time taken for a lift to transport a pallet from one floor to another
SINGLE_TRIP_LIFT_DURATION: 60
# time taken for a forklift to load a pallet at fresh storage
STORAGE_AREA_FRESH_FORKLIFT_LOAD_DURATION: 55
# time taken for a forklift to unload a pallet at fresh storage
STORAGE_AREA_FRESH_FORKLIFT_UNLOAD_DURATION: 55
# time taken for a forklift to load a pallet at ambient storage
STORAGE_AREA_AMBIENT_FORKLIFT_LOAD_DURATION: 55
# time taken for a forklift to unload a pallet at ambient storage
STORAGE_AREA_AMBIENT_FORKLIFT_UNLOAD_DURATION: 55
# time taken to travel from fresh storage to waiting area
TRAVEL_TIME_FRESH_STORAGE_TO_WAITING_AREA: 55
# time taken to travel from ambient storage to waiting area
TRAVEL_TIME_AMBIENT_STORAGE_TO_WAITING_AREA: 55
# time taken to travel from lift to waiting area
TRAVEL_TIME_LIFT_TO_WAITING_AREA: 55
# time taken for a forklift to load a pallet at DC
DC_FORKLIFT_LOAD_DURATION: 55
# time taken for a forklift to load a pallet at DSD
DSD_FORKLIFT_LOAD_DURATION: 55

# number of forklifts that can perform the inbound operation at DC
DC_FORKLIFT_OPERATION_LIMIT: 1
# number of forklifts that can perform the inbound operation at DSD
DSD_FORKLIFT_OPERATION_LIMIT: 3
# number of forklifts that can pass through the passageway at same time
PASSAGEWAY_FORKLIFT_TRHROUGHPUT_LIMIT: 4
# number of forklifts that can pass through the passageway at same time for outbound operation
PASSAGEWAY_FORKLIFT_TRHOUGHPUT_LIMIT_OUTBOUND: 1
# number of forklifts that can enter the lift area
LIFT_AREA_FORKLIFT_LIMIT: 2
# number of forklifts that can enter the lift area for outbound operation
LIFT_AREA_FORKLIFT_LIMIT_OUTBOUND: 1
# number of forklifts that can enter the fresh storage
STORAGE_AREA_FRESH_FORKLIFT_LIMIT: 4
# number of forklifts that can enter the fresh storage for outbound operation
STORAGE_AREA_FRESH_FORKLIFT_LIMIT_OUTBOUND: 1
# number of forklifts that can enter the ambient storage
STORAGE_AREA_AMBIENT_FORKLIFT_LIMIT: 4
# number of forklifts that can enter the ambient storage for outbound operation
STORAGE_AREA_AMBIENT_FORKLIFT_LIMIT_OUTBOUND: 1
# number of forklifts that can perform the outbound operation at DSD
DSD_FORKLIFT_OUTBOUND_OPERATION_LIMIT: 2
# number of forklifts that can perform the outbound operation at DC
DC_FORKLIFT_OUTBOUND_OPERATION_LIMIT: 1
# number of forklifts that can enter the DSD outbound area at unloading area
DSD_OUTBOUND_AREA_FORKLIFT_LIMIT: 2
# number of forklifts that can enter the DC outbound area at unloading area
DC_OUTBOUND_AREA_FORKLIFT_LIMIT: 1
# number of forklifts that can be used for outbound operation
FORKLIFT_LIMIT_OUTBOUND: 1

# number of pallets that can be stored in fresh storage area
STORAGE_AREA_FRESH_PALLET_CAPACITY: 9999
# number of pallets that can be stored in ambient storage area
STORAGE_AREA_AMBIENT_PALLET_CAPACITY: 9999

# if the lifts are required for ambient storage
IS_AMBIENT_LIFT_REQUIRED: True
# if the lifts are required for fresh storage
IS_FRESH_LIFT_REQUIRED: False
# number of pallets a lift can transport at a time
LIFT_PALLET_CAPACITY: 1
# total number of lifts
LIFT_COUNT: 3
# lifts that can be used for outbound operation
LIFT_USAGE_LIMIT_OUTBOUND: 2
# lifts that can be used for inbound operation
LIFT_USAGE_LIMIT_INBOUND: 3