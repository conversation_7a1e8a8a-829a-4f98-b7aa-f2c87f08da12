"""
Configuration module for warehouse unloading simulation.
Handles loading and parsing data from the configuration yaml file.

created on 12.04.2025 by <PERSON><PERSON><PERSON>
updated on 20.07.2025
"""

import yaml
from datetime import datetime

class ConfigParser:
    """
    Configuration class for the warehouse simulation.
    Contains all the configuration parameters for the simulation.
    """

    def __init__(self):
        # runtime of the simulation in seconds
        self.SIMULATION_START_TIME: datetime
        # number of DC docks
        self.DC_DOCKS: int
        # number of DSD docks
        self.DSD_DOCKS: int
        # time taken for a forklift to unload a pallet from truck
        self.DC_FORKLIFT_UNLOAD_DURATION: int
        # time taken for a forklift to unload a pallet from truck
        self.DSD_FORKLIFT_UNLOAD_DURATION: int
        # time taken to check pallets after loading/ unloading to/ from truck
        self.DSD_PALLET_COUNT_CHECK_DURATION: int
        # time taken to travel from DC to fresh storage
        self.TRAVEL_TIME_DC_TO_FRESH_STORAGE: int
        # time taken to travel from DC to ambient storage
        self.TRAVEL_TIME_DC_TO_AMBIENT_STORAGE: int
        # time taken to travel from DC to lift area
        self.TRAVEL_TIME_DC_TO_LIFT: int
        # time taken to travel from DC to lift area
        self.TRAVEL_TIME_DSD_TO_LIFT: int
        # time taken to travel from DSD to fresh storage
        self.TRAVEL_TIME_DSD_TO_FRESH_STORAGE: int
        # time taken to travel from DSD to ambient storage
        self.TRAVEL_TIME_DSD_TO_AMBIENT_STORAGE: int
        # time taken for a forklift to load a pallet to lift
        self.LIFT_FORKLIFT_LOAD_DURATION: int
        # time taken for a forklift to unload a pallet from lift
        self.LIFT_FORKLIFT_UNLOAD_DURATION: int
        # time taken for a forklift to transport a pallet from lift to fresh storage
        self.LIFT_FORKLIFT_TRANSPORT_DURATION_BUFFER_FRESH: int
        # time taken for a forklift to transport a pallet from lift to ambient storage
        self.LIFT_FORKLIFT_TRANSPORT_DURATION_BUFFER_AMBIENT: int
        # time taken for a lift to transport a pallet from one floor to another
        self.SINGLE_TRIP_LIFT_DURATION: int
        # time taken for a forklift to load a pallet at fresh storage
        self.STORAGE_AREA_FRESH_FORKLIFT_LOAD_DURATION: int
        # time taken for a forklift to unload a pallet at fresh storage
        self.STORAGE_AREA_FRESH_FORKLIFT_UNLOAD_DURATION: int
        # time taken for a forklift to load a pallet at ambient storage
        self.STORAGE_AREA_AMBIENT_FORKLIFT_LOAD_DURATION: int
        # time taken for a forklift to unload a pallet at ambient storage
        self.STORAGE_AREA_AMBIENT_FORKLIFT_UNLOAD_DURATION: int
        # time taken to travel from fresh storage to waiting area
        self.TRAVEL_TIME_FRESH_STORAGE_TO_WAITING_AREA: int
        # time taken to travel from ambient storage to waiting area
        self.TRAVEL_TIME_AMBIENT_STORAGE_TO_WAITING_AREA: int
        # time taken to travel from lift to waiting area
        self.TRAVEL_TIME_LIFT_TO_WAITING_AREA: int
        # time taken for a forklift to load a pallet at DC
        self.DC_FORKLIFT_LOAD_DURATION: int
        # time taken for a forklift to load a pallet at DSD
        self.DSD_FORKLIFT_LOAD_DURATION: int
        # number of forklifts that can perform the inbound operation at DC
        self.DC_FORKLIFT_OPERATION_LIMIT: int
        # number of forklifts that can perform the inbound operation at DSD
        self.DSD_FORKLIFT_OPERATION_LIMIT: int
        # number of forklifts that can pass through the passageway at same time
        self.PASSAGEWAY_FORKLIFT_TRHROUGHPUT_LIMIT: int
        # number of forklifts that can enter the lift area
        self.LIFT_AREA_FORKLIFT_LIMIT: int
        # number of forklifts that can enter the fresh storage
        self.STORAGE_AREA_FRESH_FORKLIFT_LIMIT: int
        # number of forklifts that can enter the ambient storage
        self.STORAGE_AREA_AMBIENT_FORKLIFT_LIMIT: int
        # number of forklifts that can perform the outbound operation at DSD
        self.DSD_FORKLIFT_OUTBOUND_OPERATION_LIMIT: int
        # number of forklifts that can perform the outbound operation at DC
        self.DC_FORKLIFT_OUTBOUND_OPERATION_LIMIT: int
        # number of forklifts that can pass through the passageway at same time for outbound operation
        self.PASSAGEWAY_FORKLIFT_TRHOUGHPUT_LIMIT_OUTBOUND: int
        # number of forklifts that can enter the fresh storage for outbound operation
        self.STORAGE_AREA_FRESH_FORKLIFT_LIMIT_OUTBOUND: int
        # number of forklifts that can enter the ambient storage for outbound operation
        self.STORAGE_AREA_AMBIENT_FORKLIFT_LIMIT_OUTBOUND: int
        # number of forklifts that can enter the lift area for outbound operation
        self.LIFT_AREA_FORKLIFT_LIMIT_OUTBOUND: int
        # number of forklifts that can be used for outbound operation
        self.FORKLIFT_LIMIT_OUTBOUND: int
        # number of pallets that can be stored in fresh storage area
        self.STORAGE_AREA_FRESH_PALLET_CAPACITY: int
        # number of pallets that can be stored in ambient storage area
        self.STORAGE_AREA_AMBIENT_PALLET_CAPACITY: int
        # if the lifts are required for ambient storage
        self.IS_AMBIENT_LIFT_REQUIRED: bool
        # if the lifts are required for fresh storage
        self.IS_FRESH_LIFT_REQUIRED: bool
        # number of pallets a lift can transport at a time
        self.LIFT_PALLET_CAPACITY: int
        # total number of lifts
        self.LIFT_COUNT: int
        # lifts that can be used for outbound operation
        self.LIFT_USAGE_LIMIT_OUTBOUND: int
        # lifts that can be used for inbound operation
        self.LIFT_USAGE_LIMIT_INBOUND: int

    def pass_yaml(self, yaml_file_path: str) -> None:
        """Pass a YAML file to the Config object."""

        with open(yaml_file_path, 'r') as file:
            yaml_data = yaml.safe_load(file)
            
            self.SIMULATION_START_TIME = datetime.strptime(yaml_data["SIMULATION_START_TIME"], "%H:%M:%S")
            self.DC_DOCKS = int(yaml_data["DC_DOCKS"])
            self.DSD_DOCKS = int(yaml_data["DSD_DOCKS"])
            self.DC_FORKLIFT_UNLOAD_DURATION = int(yaml_data["DC_FORKLIFT_UNLOAD_DURATION"])
            self.DSD_FORKLIFT_UNLOAD_DURATION = int(yaml_data["DSD_FORKLIFT_UNLOAD_DURATION"])
            self.DSD_PALLET_COUNT_CHECK_DURATION = int(yaml_data["DSD_PALLET_COUNT_CHECK_DURATION"])
            self.TRAVEL_TIME_DC_TO_FRESH_STORAGE = int(yaml_data["TRAVEL_TIME_DC_TO_FRESH_STORAGE"])
            self.TRAVEL_TIME_DC_TO_AMBIENT_STORAGE = int(yaml_data["TRAVEL_TIME_DC_TO_AMBIENT_STORAGE"])
            self.TRAVEL_TIME_DC_TO_LIFT = int(yaml_data["TRAVEL_TIME_DC_TO_LIFT"])
            self.TRAVEL_TIME_DSD_TO_LIFT = int(yaml_data["TRAVEL_TIME_DSD_TO_LIFT"])
            self.TRAVEL_TIME_DSD_TO_FRESH_STORAGE = int(yaml_data["TRAVEL_TIME_DSD_TO_FRESH_STORAGE"])
            self.TRAVEL_TIME_DSD_TO_AMBIENT_STORAGE = int(yaml_data["TRAVEL_TIME_DSD_TO_AMBIENT_STORAGE"])
            self.LIFT_FORKLIFT_LOAD_DURATION = int(yaml_data["LIFT_FORKLIFT_LOAD_DURATION"])
            self.LIFT_FORKLIFT_UNLOAD_DURATION = int(yaml_data["LIFT_FORKLIFT_UNLOAD_DURATION"])
            self.LIFT_FORKLIFT_TRANSPORT_DURATION_BUFFER_FRESH = int(yaml_data["LIFT_FORKLIFT_TRANSPORT_DURATION_BUFFER_FRESH"])
            self.LIFT_FORKLIFT_TRANSPORT_DURATION_BUFFER_AMBIENT = int(yaml_data["LIFT_FORKLIFT_TRANSPORT_DURATION_BUFFER_AMBIENT"])
            self.SINGLE_TRIP_LIFT_DURATION = int(yaml_data["SINGLE_TRIP_LIFT_DURATION"])
            self.STORAGE_AREA_FRESH_FORKLIFT_LOAD_DURATION = int(yaml_data["STORAGE_AREA_FRESH_FORKLIFT_LOAD_DURATION"])
            self.STORAGE_AREA_FRESH_FORKLIFT_UNLOAD_DURATION = int(yaml_data["STORAGE_AREA_FRESH_FORKLIFT_UNLOAD_DURATION"])
            self.STORAGE_AREA_AMBIENT_FORKLIFT_LOAD_DURATION = int(yaml_data["STORAGE_AREA_AMBIENT_FORKLIFT_LOAD_DURATION"])
            self.STORAGE_AREA_AMBIENT_FORKLIFT_UNLOAD_DURATION = int(yaml_data["STORAGE_AREA_AMBIENT_FORKLIFT_UNLOAD_DURATION"])
            self.TRAVEL_TIME_FRESH_STORAGE_TO_WAITING_AREA = int(yaml_data["TRAVEL_TIME_FRESH_STORAGE_TO_WAITING_AREA"])
            self.TRAVEL_TIME_AMBIENT_STORAGE_TO_WAITING_AREA = int(yaml_data["TRAVEL_TIME_AMBIENT_STORAGE_TO_WAITING_AREA"])
            self.TRAVEL_TIME_LIFT_TO_WAITING_AREA = int(yaml_data["TRAVEL_TIME_LIFT_TO_WAITING_AREA"])
            self.DC_FORKLIFT_LOAD_DURATION = int(yaml_data["DC_FORKLIFT_LOAD_DURATION"])
            self.DSD_FORKLIFT_LOAD_DURATION = int(yaml_data["DSD_FORKLIFT_LOAD_DURATION"])
            self.DC_FORKLIFT_OPERATION_LIMIT = int(yaml_data["DC_FORKLIFT_OPERATION_LIMIT"])
            self.DSD_FORKLIFT_OPERATION_LIMIT = int(yaml_data["DSD_FORKLIFT_OPERATION_LIMIT"])
            self.PASSAGEWAY_FORKLIFT_TRHROUGHPUT_LIMIT = int(yaml_data["PASSAGEWAY_FORKLIFT_TRHROUGHPUT_LIMIT"])
            self.LIFT_AREA_FORKLIFT_LIMIT = int(yaml_data["LIFT_AREA_FORKLIFT_LIMIT"])
            self.STORAGE_AREA_FRESH_FORKLIFT_LIMIT = int(yaml_data["STORAGE_AREA_FRESH_FORKLIFT_LIMIT"])
            self.STORAGE_AREA_AMBIENT_FORKLIFT_LIMIT = int(yaml_data["STORAGE_AREA_AMBIENT_FORKLIFT_LIMIT"])
            self.DSD_FORKLIFT_OUTBOUND_OPERATION_LIMIT = int(yaml_data["DSD_FORKLIFT_OUTBOUND_OPERATION_LIMIT"])
            self.DC_FORKLIFT_OUTBOUND_OPERATION_LIMIT = int(yaml_data["DC_FORKLIFT_OUTBOUND_OPERATION_LIMIT"])
            self.PASSAGEWAY_FORKLIFT_TRHOUGHPUT_LIMIT_OUTBOUND = int(yaml_data["PASSAGEWAY_FORKLIFT_TRHOUGHPUT_LIMIT_OUTBOUND"])
            self.STORAGE_AREA_FRESH_FORKLIFT_LIMIT_OUTBOUND = int(yaml_data["STORAGE_AREA_FRESH_FORKLIFT_LIMIT_OUTBOUND"])
            self.STORAGE_AREA_AMBIENT_FORKLIFT_LIMIT_OUTBOUND = int(yaml_data["STORAGE_AREA_AMBIENT_FORKLIFT_LIMIT_OUTBOUND"])
            self.LIFT_AREA_FORKLIFT_LIMIT_OUTBOUND = int(yaml_data["LIFT_AREA_FORKLIFT_LIMIT_OUTBOUND"])
            self.FORKLIFT_LIMIT_OUTBOUND = int(yaml_data["FORKLIFT_LIMIT_OUTBOUND"])
            self.STORAGE_AREA_FRESH_PALLET_CAPACITY = int(yaml_data["STORAGE_AREA_FRESH_PALLET_CAPACITY"])
            self.STORAGE_AREA_AMBIENT_PALLET_CAPACITY = int(yaml_data["STORAGE_AREA_AMBIENT_PALLET_CAPACITY"])
            self.IS_AMBIENT_LIFT_REQUIRED = bool(yaml_data["IS_AMBIENT_LIFT_REQUIRED"])
            self.IS_FRESH_LIFT_REQUIRED = bool(yaml_data["IS_FRESH_LIFT_REQUIRED"])
            self.LIFT_PALLET_CAPACITY = int(yaml_data["LIFT_PALLET_CAPACITY"])
            self.LIFT_COUNT = int(yaml_data["LIFT_COUNT"])
            self.LIFT_USAGE_LIMIT_OUTBOUND = int(yaml_data["LIFT_USAGE_LIMIT_OUTBOUND"])
            self.LIFT_USAGE_LIMIT_INBOUND = int(yaml_data["LIFT_USAGE_LIMIT_INBOUND"])               

# Example usage
if __name__ == "__main__":
    config = ConfigParser()
    config.pass_yaml("src/config/config.yaml")

    # test the config
    print(config.SIMULATION_START_TIME.hour)
    print(config.DC_DOCKS)
    print(config.IS_AMBIENT_LIFT_REQUIRED)
    print(config.IS_FRESH_LIFT_REQUIRED)
    print(config.LIFT_COUNT)
